import { useI18n } from 'vue-i18n'

/**
 * 生成带有中英文标识的文件名
 * @param baseName 基础文件名
 * @param extension 文件扩展名
 * @param includeTimestamp 是否包含时间戳
 * @returns 格式化的文件名
 */
export function generateBilingualFileName(
  baseName: string,
  extension: string,
  includeTimestamp: boolean = true
): string {
  const { locale, t } = useI18n()
  const currentLang = locale.value
  
  // 获取当前语言的文件名
  const localizedBaseName = t(baseName) || baseName
  
  // 生成时间戳
  const timestamp = includeTimestamp 
    ? `_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')}`
    : ''
  
  // 根据当前语言生成文件名
  if (currentLang === 'cn') {
    return `${localizedBaseName}_CN${timestamp}.${extension}`
  } else {
    return `${localizedBaseName}_EN${timestamp}.${extension}`
  }
}

/**
 * 下载文件的通用函数，支持中英文文件名
 * @param url 文件URL
 * @param baseName 基础文件名（支持国际化key）
 * @param extension 文件扩展名
 * @param includeTimestamp 是否包含时间戳
 */
export function downloadFileWithBilingualName(
  url: string,
  baseName: string,
  extension: string,
  includeTimestamp: boolean = true
): void {
  const fileName = generateBilingualFileName(baseName, extension, includeTimestamp)
  
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 创建并下载Blob文件，支持中英文文件名
 * @param content 文件内容
 * @param baseName 基础文件名（支持国际化key）
 * @param extension 文件扩展名
 * @param mimeType MIME类型
 * @param includeTimestamp 是否包含时间戳
 */
export function downloadBlobWithBilingualName(
  content: string | Blob,
  baseName: string,
  extension: string,
  mimeType: string = 'text/plain',
  includeTimestamp: boolean = true
): void {
  const fileName = generateBilingualFileName(baseName, extension, includeTimestamp)
  
  const blob = content instanceof Blob 
    ? content 
    : new Blob([content], { type: mimeType })
  
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

/**
 * 为上传的文件添加中英文前缀
 * @param originalFileName 原始文件名
 * @returns 带有语言标识的文件名
 */
export function addLanguagePrefixToFileName(originalFileName: string): string {
  const { locale } = useI18n()
  const currentLang = locale.value
  
  const langPrefix = currentLang === 'cn' ? 'CN_' : 'EN_'
  
  // 如果文件名已经有语言前缀，则不重复添加
  if (originalFileName.startsWith('CN_') || originalFileName.startsWith('EN_')) {
    return originalFileName
  }
  
  return `${langPrefix}${originalFileName}`
}

/**
 * 从文件名中提取语言标识
 * @param fileName 文件名
 * @returns 语言标识 ('cn' | 'en' | null)
 */
export function extractLanguageFromFileName(fileName: string): 'cn' | 'en' | null {
  if (fileName.startsWith('CN_') || fileName.includes('_CN')) {
    return 'cn'
  } else if (fileName.startsWith('EN_') || fileName.includes('_EN')) {
    return 'en'
  }
  return null
}

/**
 * 生成服务器端文件名，包含中英文标识
 * @param baseName 基础文件名
 * @param extension 文件扩展名
 * @param serverPrefix 服务器前缀（可选）
 * @returns 服务器端文件名
 */
export function generateServerFileName(
  baseName: string,
  extension: string,
  serverPrefix: string = 'server'
): string {
  const { locale } = useI18n()
  const currentLang = locale.value
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_')
  
  const langSuffix = currentLang === 'cn' ? 'zh' : 'en'
  
  return `${serverPrefix}_${baseName}_${langSuffix}_${timestamp}.${extension}`
}
